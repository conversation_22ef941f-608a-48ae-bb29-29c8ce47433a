<template>
  <div class="web-container" style="padding-bottom: 30px;">
    <el-row style="height: inherit;flex: 1;">
      <!--部门数据-->
      <el-col :span="4" :xs="24" style="height: inherit;">
        <dept-tree
          :station-status="true"
          :dept-name-query="deptName"
          :station-list.sync="stationList"
          @nodeClick="handleNodeClick"
        />
      </el-col>


      <el-col :span="20" :xs="24" class="web-container" style="padding: 0;height: inherit;">

        <!-- 搜索工作栏 -->
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 :label-width="SearchFormWidth.SMALL" :rules="rules">
          <el-form-item label="状态" prop="fstatus">
            <el-select
              v-model="queryParams.fstatus"
              clearable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.PRO_DISPATCH_STATUS)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="销号状态" prop="fxhStatus">
            <el-select
              v-model="queryParams.fxhStatus"
              clearable
              placeholder="请选择销号状态"
            >
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.PRO_PLAN_DISPATCH_FISWORK)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="车间确认" prop="fflag">
            <el-select
              v-model="queryParams.fflag"
              clearable
              placeholder="请选择状态"
              sortable
            >
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.PRO_PLAN_DISPATCH_FFLAG)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="作业日期" prop="fdate" :label-width="LabelWidth.MIDDLE">
            <el-date-picker
              v-model="queryParams.fdate"
              style="width: 240px"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期" end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              :key="dataPickerKey"
              :picker-options="{ firstDayOfWeek: 1 }"
            />
          </el-form-item>
          <el-form-item label="车站/区间" prop="fstationNames" :label-width="LabelWidth.MIDDLE">
            <el-select v-model="queryParams.fstationNames" clearable style="width: 95%;" filterable
                       placeholder="请选择车站/区间">
              <el-option
                v-for="item in stationList"
                :key="item.fname"
                :label="item.fname"
                :value="item.fname"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="人员" prop="fperson">
            <el-input v-model="queryParams.fperson" clearable style="width: 95%;"
                      placeholder="请输入人员">
            </el-input>
          </el-form-item>
          <el-form-item label="作业内容" prop="frepairEqu">
            <el-input v-model="queryParams.frepairEqu" placeholder="请输入作业内容" clearable/>
          </el-form-item>

          <el-form-item label="派工单类型" prop="fworkKind" label-width="90px">
            <el-select
              v-model="queryParams.fworkKind"
              clearable
              placeholder="请选择派工单类型"
            >
              <el-option
                v-for="item in this.worktypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="作业项目" prop="fworkItem">
            <el-select
              multiple
              v-model="queryParams.fworkItemList"
              clearable
              placeholder="请选择作业项目"
            >
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.PRO_PLAN_DAY_DISPATCH_WORK_PROJECT)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="计划号" prop="ftaskSource">
            <el-input v-model="queryParams.ftaskSource" placeholder="请输入计划号" clearable/>
          </el-form-item>
          <el-form-item label="调度命令" prop="fhasFscId" v-if="!columnList.includes('fscNo')">
            <el-select
              v-model="queryParams.fhasFscId"
              clearable
              placeholder="请选择是否申请调度命令"
            >
              <el-option
                v-for="item in this.fscIdType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="盯控" prop="fhasDk" v-if="!columnList.includes('fwmNo')">
            <el-select
              v-model="queryParams.fhasDk"
              clearable
              placeholder="请选择是否申请盯控号"
            >
              <el-option
                v-for="item in this.fscIdType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="风险等级" prop="fdangerLevel" v-if="!columnList.includes('fdangerLevel')">
            <el-select v-model="queryParams.fdangerLevel" placeholder="请选择风险等级" clearable size="small">
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.PRO_DANGER_LEVEL)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="作业等级" prop="fdispatchLevel" v-if="!columnList.includes('fdispatchLevel')">
            <el-select v-model="queryParams.fdispatchLevel" placeholder="请选择作业等级" clearable size="small">
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.PRO_DISPATCH_LEVEL)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8">
          <!--      流程待定所以隐藏审核按钮  2024-04-25 lc edit-->
          <!--<el-col :span="1.5">-->
          <!--  <el-button type="primary" plain size="mini" @click="handleExamine"-->
          <!--             v-hasPermi="['produce:plan-day:examine']">审核-->
          <!--  </el-button>-->
          <!--</el-col>-->
          <el-col :span="1.5">
            <el-button type="primary" size="mini" icon="el-icon-plus"
                       @click="handleAdd()"
                       v-hasPermi="['produce:plan-dispatch:new']">新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="mini" @click="handleEdit('edit')"
                       v-hasPermi="['produce:dispatch-detail:update']">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="mini" @click="handleEdit('look')">查看
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="mini" icon="el-icon-top"
                       @click="handleExamine('produce:plan-dispatch:submit', '0')"
                       v-hasPermi="['produce:plan-dispatch:submit']">提交
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" size="mini" icon="el-icon-bottom"
                       @click="handleRecall"
                       v-hasPermi="['produce:plan-dispatch:recall-submit']">撤回提交
            </el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-document"
              @click="handleBatchExamine('produce:plan-dispatch:batchExamine')"
              v-hasPermi="['produce:plan-dispatch:batchExamine']"
            >批量审核</el-button>
           </el-col> -->
          <el-col :span="1.5">
            <el-button type="primary" size="mini" icon="el-icon-document"
                       @click="handleExamine('produce:plan-dispatch:examine', '1')"
                       v-hasPermi="['produce:plan-dispatch:examine']">审核
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="mini" icon="el-icon-document"
                       @click="handleExamine('produce:plan-dispatch:approval', '2')"
                       v-hasPermi="['produce:plan-dispatch:approval']">审批
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="mini" icon="el-icon-document"
                       @click="handleExamine('produce:plan-dispatch:ratify', '3')"
                       v-hasPermi="['produce:plan-dispatch:ratify']">审批1
            </el-button>
          </el-col>
          <!--      <el-col :span="1.5">-->
          <!--        <el-button icon="el-icon-view" type="primary" plain size="mini" @click="handleShowDispatch">查看派工单-->
          <!--        </el-button>-->
          <!--      </el-col>-->
          <!--      <el-col :span="1.5">-->
          <!--        <el-button icon="el-icon-view" type="primary" plain size="mini" @click="handleShow">查看现场防护单-->
          <!--        </el-button>-->
          <!--      </el-col>-->
          <!--      <el-col :span="1.5">-->
          <!--        <el-button icon="el-icon-view" type="primary" plain size="mini" @click="handleShowZZ">查看驻站防护单-->
          <!--        </el-button>-->
          <!--      </el-col>-->
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteAll"
                       v-hasPermi="['produce:dispatch:delete']">删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-folder-delete" size="mini" @click="handleCancelAll"
                       v-hasPermi="['produce:dispatch:delete']">作废
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="mini" icon="el-icon-top"
                       v-hasPermi="['produce:schedule-command:update']"
                       @click="handleApproveCommand">申请命令
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="mini" icon="el-icon-top"
                       v-hasPermi="['produce:dispatch:approve-monitor']"
                       @click="handleApproveMonitor">申请盯控
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="mini" icon="el-icon-document-checked"
                       v-hasPermi="['produce:dispatch-detail:update']"
                       @click="handleXH">销号
            </el-button>
          </el-col>
          <!--      <el-col :span="1.5">-->
          <!--        <el-button type="primary" plain size="mini" @click="handleCash"-->
          <!--                   v-hasPermi="['produce:plan-day:create']">兑现（弃用）-->
          <!--        </el-button>-->
          <!--      </el-col>-->
          <el-col :span="1.5">
            <el-button type="primary" size="mini"
                       v-hasPermi="['produce:dispatch:workshop-confirm']"
                       @click="handleConfirm('confirm')" icon="el-icon-circle-check">车间确认
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="mini"
                       v-hasPermi="['produce:dispatch:workshop-confirm']"
                       @click="handleConfirm('cancel')" icon="el-icon-circle-close">取消确认
            </el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="primary" size="mini"
                       @click="handleShowCash" icon="el-icon-view">兑现情况
            </el-button>
          </el-col> -->
          <!--      变更审批人员 第一步添加按钮-->
          <el-col :span="1.5">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-document"
              v-hasPermi="['produce:dispatch-detail:change']"
              @click="handleChangeApproval()"
              class="table-tool-bar"
            >变更审批人
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                       :loading="exportLoading">导出
            </el-button>
          </el-col>
          <!--    变更审批人员 第二步添加弹出选择人员-->
          <base-audit-user-transfer ref="BaseAuditUserTransfer"></base-audit-user-transfer>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 列表 -->
        <el-table
          highlight-current-row
          border
          ref="multipleTable"
          v-loading="loading"
          :data="list"
          :height="tableH - 30"
          class="web-table-flex"
          @selection-change="handleSelectionChange"
          @row-dblclick="handleApprovalProcess"
          @row-click="handleRowSelectClick(arguments, multipleSelection, $refs.multipleTable)"
          @sort-change="tableSortChange(arguments, list, getList)"
          :span-method="spanMethod"
        >
          <el-table-column type="selection" :width="CheckBoxWidth"/>
          <!-- 将 type="index" 改为自定义序号列 -->
          <el-table-column label="序号" width="50" prop="customIndex">
            <template slot-scope="scope">
              {{ getCustomIndex(scope.$index) }}
            </template>
          </el-table-column>
          <el-table-column label="作业号" align="center" prop="fdispatchNo" min-width="110"
                           v-if="!columnList.includes('fdispatchNo')">
          </el-table-column>
          <el-table-column label="状态" align="center" prop="fstatus" width="80" sortable="custom"
                           v-if="!columnList.includes('fstatus')">
            <template v-slot="scope">
              <div class="status-column">
                <el-badge type="warning" :value="scope.row.fsyncFlag==='Y'?'日':''">
                  <dict-tag
                    :type="DICT_TYPE.PRO_DISPATCH_STATUS"
                    :value="scope.row.fstatus"
                  />
                </el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="派工单编号" align="center" prop="fdisMainNo" min-width="110"
                           v-if="!columnList.includes('fdisMainNo')">
            <template v-slot="scope">
            <span style="cursor: pointer;color: blue;"
                  @click="handleShowDis(scope.row)">{{ scope.row.fdisMainNo }}</span>
            </template>
          </el-table-column>
          <el-table-column label="预览" align="center" width="70">
            <template v-slot="scope">
              <el-button type="primary" size="mini" plain
                         @click="handleShowDispatch(scope.row)">预览
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="销号状态" align="center" prop="fxhStatus" width="90"
                           v-if="!columnList.includes('fxhStatus')">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.PRO_PLAN_DISPATCH_FISWORK"
                :value="scope.row.fxhStatus"
              />
            </template>
          </el-table-column>
          <el-table-column label="车间确认" align="center" prop="fflag" width="90" v-if="!columnList.includes('fflag')">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.PRO_PLAN_DISPATCH_FFLAG"
                :value="scope.row.fflag"
              />
            </template>
          </el-table-column>
          <el-table-column label="风险等级" align="center" prop="fdangerLevel" min-width="80"
                           v-if="!columnList.includes('fdangerLevel')">
            <template v-slot="scope">
              <span v-html="setDangerLevel(scope.row)"/>
            </template>
          </el-table-column>
          <el-table-column label="命令号" align="center" prop="fscNo"
                           width="110" v-if="!columnList.includes('fscNo')">
            <template v-slot="scope">
              <span style="cursor: pointer;color: blue;"
                    @click="onClick(scope.row)">{{ setFcsNo(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="盯控号" align="center" prop="fwmNo"
                           width="110" v-if="!columnList.includes('fwmNo')">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.PRO_WORK_MONITOR_STATUS"
                :value="scope.row.fwmStatus"
              />
              <span style="cursor: pointer;color: blue;"
                    @click="onWmNoClick(scope.row)">{{ setFwmNo(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column width="150" label="部门" align="center" prop="fdeptName" sortable
                           v-if="!columnList.includes('fdeptName')"/>
          <el-table-column label="车站" align="center" prop="fstationNames" width="150" sortable="custom"
                           v-if="!columnList.includes('fstationNames')"/>
          <el-table-column label="作业日期" align="center" prop="fdate" width="110" sortable
                           v-if="!columnList.includes('fdate')">
            <template v-slot="scope">
              <span>{{ parseTime(scope.row.fdate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="作业时间" align="center" prop="ftime" width="110" sortable
                           v-if="!columnList.includes('ftime')">
            <template v-slot="scope">
              <span>{{ parseTime(scope.row.ftimeBegin, '{h}:{i}') }}-{{
                  parseTime(scope.row.ftimeEnd, '{h}:{i}')
                }}</span>
            </template>
          </el-table-column>
          <el-table-column label="负责人" align="center" prop="fchargeperson" width="150" sortable="custom"
                           v-if="!columnList.includes('fchargeperson')"/>
          <el-table-column label="组长" align="center" prop="fleaderNames" width="150" sortable="custom"
                           v-if="!columnList.includes('fleaderNames')"/>
          <el-table-column label="计划号" align="center" prop="ftaskSource" width="150" sortable="custom"
                           v-if="!columnList.includes('ftaskSource')">
            <template v-slot="scope">
              <span class="scroll-span" v-if="['1','2','9'].includes(scope.row.fworkKind)"
                    style="cursor: pointer;color: blue;" @click="showFplan( scope.row)">{{
                  scope.row.ftaskSource
                }}</span>
              <span class="scroll-span" v-else>{{ scope.row.ftaskSource }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="flevel"
            align="center"
            width="80"
            label="等级"
            v-if="!columnList.includes('flevel')"
          >
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.PRO_BASE_YEAR_LEVEL"
                :value="scope.row.flevel"
              />
            </template>
          </el-table-column> -->
          <el-table-column label="作业内容" align="center" prop="frepairEqu" min-width="250" sortable="custom"
                           v-if="!columnList.includes('frepairEqu')">
            <template v-slot="scope">
              <span class="scroll-span">{{ scope.row.frepairEqu }}</span>
            </template>
          </el-table-column>
          <el-table-column label="负责人类型" align="center" prop="frespType" width="120" sortable="custom"
                           v-if="!columnList.includes('frespType')">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.PRO_PLAN_DAY_DISPATCH_FRESP_TYPE"
                :value="scope.row.frespType"
              />
            </template>
          </el-table-column>
          <el-table-column label="负责人职务" align="center" prop="frespJob" width="120" sortable="custom"
                           v-if="!columnList.includes('frespJob')"/>
          <el-table-column label="作业项目" align="center" prop="fworkItem" width="120" sortable="custom"
                           v-if="!columnList.includes('fworkItem')">
            <template v-slot="{row}">
              <dict-tag
                v-for="(item, index) in row?.fworkItem ? row.fworkItem.split(',') : []"
                :key="index"
                :type="DICT_TYPE.PRO_PLAN_DAY_DISPATCH_WORK_PROJECT"
                :value="item"
                :style="{ display: 'block' }"
              />
            </template>
          </el-table-column>
          <el-table-column label="是否配合防护" align="center" prop="fiscoopprotect" width="110"
                           v-if="!columnList.includes('fiscoopprotect')">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.PRO_BASE_DANGER_ISDEF"
                :value="scope.row.fiscoopprotect"
              />
            </template>
          </el-table-column>
          <el-table-column label="是否综合防护" align="center" prop="fiscomprotect" width="110"
                           v-if="!columnList.includes('fiscomprotect')">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.PRO_BASE_DANGER_ISDEF"
                :value="scope.row.fiscomprotect"
              />
            </template>
          </el-table-column>
          <el-table-column label="派工人" align="center" prop="fdispatcher" width="100" sortable="custom"
                           v-if="!columnList.includes('fdispatcher')"/>
          <el-table-column label="盯控干部" align="center" prop="fwatcher" width="120" sortable="custom"
                           v-if="!columnList.includes('fwatcher')"/>
          <el-table-column label="执法仪" align="center" prop="flawInstrument" width="180" sortable="custom"
                           v-if="!columnList.includes('flawInstrument')">
            <template v-slot="scope">
              <span class="scroll-span">{{ scope.row.flawInstrument }}</span>
            </template>
          </el-table-column>
          <el-table-column label="驻站联络员" align="center" prop="flinkNames" width="150" sortable="custom"
                           v-if="!columnList.includes('flinkNames')"/>
          <el-table-column label="室内操作人员" align="center" prop="findoorNames" width="150" sortable="custom"
                           v-if="!columnList.includes('findoorNames')"/>
          <el-table-column label="现场防护员" align="center" prop="fprotectNames" width="150" sortable="custom"
                           v-if="!columnList.includes('fprotectNames')"/>
          <el-table-column label="质量验收员" align="center" prop="fqualityerNames" width="150" sortable="custom"
                           v-if="!columnList.includes('fqualityerNames')"/>
          <el-table-column label="工作人员" align="center" prop="fworkNames" width="150"/>
          <el-table-column
            label="提交人"
            align="center"
            prop="fcompileName"
            width="100"
          />
          <el-table-column label="派工单类型" align="center" prop="fworkKind" width="150" sortable="custom"
                           v-if="!columnList.includes('fworkKind')" :formatter="useDispatchType"/>

          <el-table-column label="作业等级" align="center" prop="fdispatchLevel" width="90"
                           v-if="!columnList.includes('fdispatchLevel')">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.PRO_DISPATCH_LEVEL"
                :value="scope.row.fdispatchLevel"
              />
            </template>
          </el-table-column>

          <!--          <el-table-column width="150" fixed="right" label="操作" align="center" class-name="small-padding fixed-width">-->
          <!--            <template v-slot="scope">-->
          <!--              <el-button size="mini" type="text"-->
          <!--                         icon="el-icon-view" @click="handlePgShow(scope.row)">查看-->
          <!--              </el-button>-->
          <!--              <el-button size="mini" type="text" icon="el-icon-edit"-->
          <!--                         :disabled="((Number(scope.row.fstatus)===10)||(scope.row.fscId!==null)||(scope.row.fwmNo!==null)||-->
          <!--                     (Number(scope.row.fstatus)>0&&Number(scope.row.fstatus)<9)||scope.row.fflag==='Y'-->
          <!--                     )"-->
          <!--                         v-hasPermi="['produce:dispatch-detail:update']"-->
          <!--                         @click="handleUpdate(scope.row)">修改-->
          <!--              </el-button>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
        </el-table>
        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                    @pagination="getList"/>
      </el-col>
    </el-row>

    <!--兑现-->
    <el-dialog
      title="兑现"
      :visible.sync="cashOpen"
      width="1000px"
      v-dialogDrag
      append-to-body
      @close="cashEditOk"
    >
      <plan-day-cash ref="PlanDayCash" @editOk="cashEditOk"></plan-day-cash>
    </el-dialog>

    <!--    申请命令号-->
    <el-dialog
      title="申请命令号"
      :visible.sync="commandOpen"
      width="750px"
      v-dialogDrag
      append-to-body
      @close="commandEditOk"
    >
      <schedule-command ref="scheduleCommand" @editOk="commandEditOk"></schedule-command>
    </el-dialog>

    <!-- 申请盯控弹窗 -->
    <el-dialog
      title="申请盯控"
      :visible.sync="addWorkMonitorOpen"
      width="750px"
      v-dialogDrag
      append-to-body
      @close="AddWorkMonitorEditOk"
    >
      <add-work-monitor ref="addWorkMonitor" @editOk="AddWorkMonitorEditOk"></add-work-monitor>
    </el-dialog>

    <!--    销号弹窗-->
    <el-dialog
      :title="title"
      :visible.sync="saleOpen"
      width="1800px"
      v-dialogDrag
      append-to-body
      @close="saleEditOk"
    >
      <sales-number ref="salesNumber" @editOk="saleEditOk"></sales-number>
    </el-dialog>

    <!--    审批流程-->
    <approval-process ref="ApprovalProcess"/>

    <!-- 确认弹窗 -->
    <el-dialog
      title="车间确认"
      :visible.sync="confirmOpen"
      width="1500px"
      v-dialogDrag
      append-to-body
      @close="confirmEditOk"
    >
      <confirm-sales ref="confirmSales" @editOk="confirmEditOk"></confirm-sales>
    </el-dialog>
    <!--    预览派工单弹窗-->
    <el-dialog title="查看明细" v-dialogDrag append-to-body width="80%" :visible.sync="viewDispatchFlag"
               @close="closeAllDialog">
      <view-dispatch ref="viewDispatch" v-if="viewDispatchFlag===true"></view-dispatch>
    </el-dialog>

    <base-exam-user-transfer ref="baseExamUserTransfer" @change="handleExamine"></base-exam-user-transfer>

    <view-dispatch-order
      ref="viewDispatchOrder"
      :visible="openDispatch"
      :title="titleDispatch"
      @editOk="dispatchEditOk"
    />

    <el-dialog title="查看调度命令" v-dialogDrag append-to-body width="80%" :visible.sync="viewSecheduleFlag"
               @close="closeAllDialog">
      <view-schedule ref="viewSchedule"></view-schedule>
    </el-dialog>


    <!--    tdms维修查看页面-->
    <el-dialog title="维修计划" :visible.sync="openTdmsDetail" width="1000px" v-dialogDrag append-to-body>
      <addDetail ref="addDetail" @editOk="editOk"/>
    </el-dialog>

    <el-dialog title="施工计划" :visible.sync="openConstructionDetail" width="1000px" v-dialogDrag append-to-body>
      <add-detail-construction ref="addDetailConstruction" @editOk="editOk"/>
    </el-dialog>


    <el-dialog title="临时天窗计划" :visible.sync="openTcplanDetail" width="96%" v-dialogDrag append-to-body>
      <add-tcplan ref="addTcplanDetail" @editOk="editOk"/>
    </el-dialog>

    <!-- 对话框(添加派工单) -->
    <el-dialog :title="addDispatchTitle" :visible.sync="addDispatchOpen" width="500px" @close="handleAddDispatchClose"
               v-dialogDrag append-to-body>
      <AddDispatch ref="AddDispatch" @createDispatch="handleJump" @close="handleAddDispatchClose"/>
    </el-dialog>
  </div>
</template>

<script>
import {
  batchDeletePlanDayDispatch,
  batchInvalidPlanDayDispatch,
  exportPlanDispatchExcel,
  getPlanDayDetailListAll,
  getPlanDayDispatchPage,
  saveOrUpdatePlanDayDispatch
} from "@/api/produce/planDay";
import baseutil from "@/utils/baseutil";
import editDetail from "@/views/produce/planDay/detail";
import BaseDeptSelectTree from "@/components/BaseDeptSelectTree/index.vue";
import PlanDayCash from "@/views/produce/planDispatch/components/cash.vue";
import scheduleCommand from "@/views/produce/planDispatch/components/scheduleCommand.vue"
import SalesNumber from "@/views/produce/planDispatch/components/SalesNumber.vue";
import ConfirmSales from "@/views/produce/planDispatch/components/confirmSales.vue";
import {getAuditConfigurationListByPage} from "@/api/produce/osWeek";
import {auditVerification} from "@/utils/ruoyi";
import {baseAuditBeforeSubmit} from "@/api/produce/base/baseCalendar";
import {
  cancelWorkshopConfirm,
  getWorkMonitorByDispatchId,
  recallDispatch,
  submitDispatch
} from "@/api/produce/dispatch";
import {createScheduleCommand, getScheduleCommand} from "@/api/produce/scheduleCommand";
import {getStationIntervalListByLD} from "@/api/basedata/station";
import moment from "moment";
import ApprovalProcess from "@/components/ApprovalProcess/index.vue";
import ViewDispatch from "@/views/produce/planDispatch/components/viewDispatch.vue";
import ViewDispatchOrder from "@/components/ViewDispatchOrder/index.vue";
import ViewSchedule from "@/views/produce/planDispatch/components/viewSchedule.vue";
import {DICT_TYPE} from "@/utils/dict";
import {getConfigKey} from "@/api/system/baseConfig";
import {PROD_DISPATCH, PROD_SCHEDULE_COMMAND} from "@/utils/constants";
import addDetail from "@/views/produce/planTdms/add.vue";
import addDetailConstruction from "@/views/produce/planTdmsConstruction/add";
import addTcplan from "@/views/produce/tcplan/detailItem";
import {getPlanTdms} from "@/api/produce/planTdms";
import {getPlanTdmsConstruction} from "@/api/produce/planTdmsConstruction";
import {getTcplan} from "@/api/produce/tcplan";
import DeptTree from "@/components/DeptTree/index.vue";
import AddWorkMonitor from "@/views/produce/planDispatch/components/addWorkMonitor.vue";
import {listDispatchWorktypeDict} from "@/api/produce/dispatchWorktype";
import AddDispatch from "@/views/produce/planDispatch/components/addDispatch.vue";
import {getWorkMonitorListByIds} from "@/api/produce/workMonitor";


export default {
  name: "PlanDispatchFZ",
  components: {
    AddWorkMonitor,
    ViewSchedule,
    ViewDispatch,
    ApprovalProcess,
    ConfirmSales,
    SalesNumber,
    editDetail,
    scheduleCommand,
    BaseDeptSelectTree,
    PlanDayCash,
    ViewDispatchOrder,
    addDetail,
    addDetailConstruction,
    addTcplan,
    DeptTree,
    AddDispatch
  },
  data() {
    return {
      addDispatchOpen: false,
      addDispatchTitle: '',
      openTdmsDetail: false, //查看维修计划弹框
      openConstructionDetail: false, //查看施工计划弹框
      openTcplanDetail: false, //临时天窗查看
      FILE_ONE: null,
      protectNames: [],  //现场防护人员
      linkNames: [],  //驻站防护员
      findoorNames: [],  //室内操作人员
      title: null,
      fdwFlag: this.$store.getters.deptInfo.fdwFlag,
      fgwFlag: this.$store.getters.deptInfo.fgwFlag,
      confirmOpen: false, //车间确认弹窗
      commandOpen: false, //命令号窗口
      saleOpen: false, //销号窗口
      loading: true, // 遮罩层
      showSearch: true, // 显示搜索条件
      total: 0, // 总条数
      list: [], // 派工单主列表
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: baseutil.pagesize(),
        createTime: [],
        remark: null,
        fdate: [
          moment().startOf('day').subtract(3, 'day').format("YYYY-MM-DD HH:mm:ss"), // 前三天
          moment().startOf('day').add(3, 'days').format("YYYY-MM-DD HH:mm:ss") // 后三天
        ], // 作业日期
        fdeptId: null,  // 部门id
        fmonthId: null,
        fweekId: null,
        fmouldId: null,
        fworkareaId: null,
        fworkareaName: null,
        fworkshopId: null,
        fworkshopName: null,
        fcsdeptId: null,
        fcsdeptName: null,
        fstatus: null,
        fauditFlag: null,
        fprocessInstanceId: null,
        fstationIds: null,
        fstationNames: null,
        fflag: null,
        fmainId: null,
        fperson: null,
        frepairEqu: null,
        fworkItem: null,
        fworkItemList: [],
        ftaskSource: null,
        fhasFscId: null,
        fhasDk: null,
        fdispatchLevel: null,
      },
      // 表单参数
      form: {},
      // table动态改变高度默认值
      tableH: 500,
      multipleSelection: [],  // 选中的数据
      stationKey: '',
      cashOpen: false, // 兑现弹窗
      //调用审批流程需要的参数
      bigForm: {
        id: "",
        fywType: "ScheduleCommand",
        fpage: "ScheduleCommandApproval",
        fparam: "",
        fstatus: null,
        ffstatus: "0",
        fopinion: "",
        fopinionType: "Y",
        fdeptId: this.$store.getters.deptInfo.id,
        finfoDeptId: this.$store.getters.deptInfo.id,
        fdeptName: this.$store.getters.deptInfo.name,
        fuserId: this.$store.getters.userId,
        fuserName: this.$store.getters.nickname,
        remark: "",
        data: {
          fsubmitLevel: "",
        }
      },
      examConfig: [],
      stationNames: null,
      stationList: [],
      exportLoading: false,
      dataPickerKey: 0,
      viewDispatchFlag: false,
      viewSecheduleFlag: false,
      openDispatch: false,
      titleDispatch: '',
      fscIdType: [{label: '是', value: 'Y'},
        {label: '否', value: 'N'}],
      fconfig: [],
      rules: {
        fdate: [{required: true, message: '查询条件作业日期不能为空', trigger: 'change'}]
      },
      columnList: [],
      styleClass: 'CD',
      deptName: undefined,
      addWorkMonitorOpen: false,
      worktypeList: [],
      enableFlag: {
        fschedule: null,
        monitor: null
      },
      spanArrMap: {} // 每个字段都有自己的 spanArr
    };
  },
  activated() {
    // this.queryParams.fmainId = this.$route.query.id;
    this.deptName = undefined;
    const queryData = this.$route.params
    if (queryData.id) {
      const date = moment(queryData.fdate);
      const formattedDate = date.format('YYYY-MM-DD');
      this.queryParams.fdate[0] = `${formattedDate} 00:00:00`
      this.queryParams.fdate[1] = `${formattedDate} 23:59:59`
      this.queryParams.fmainId = queryData.id
      // this.queryParams.fdeptId = queryData.fworkareaId
      if (queryData.fworkareaName) {
        this.deptName = queryData.fworkareaName
        // this.$refs.tree.filter(this.deptName);
      }
      this.dataPickerKey = Date.now()
      // this.defaultExpandedKeys.push(queryData.fworkareaId)
    }
    this.getdispatchWorktype();
    this.getList();
    // this.getConfigData();
    this.getAuditConfigurationByPage();
  },
  created() {
    // this.generateDeptTree()
    getConfigKey(this.$store.getters.deptInfo.id, 'prod.plan-dispatch.style').then(res => {
      if (res.data) {
        let data = JSON.parse(res.data);
        this.columnList = data?.column?.split(',') || [];
        this.styleClass = data?.styleClass || 'CD';
        this.$nextTick(() => {
          this.$refs.multipleTable.doLayout();
        })
      }
    });
    getConfigKey(this.$store.getters.deptInfo.id, PROD_DISPATCH.ENABLE_SCHEDULE).then(res => {
      if (res && res.data) {
        this.enableFlag.fschedule = res.data;
      }
    })
    getConfigKey(this.$store.getters.deptInfo.id, PROD_DISPATCH.ENABLE_MONITOR).then(res => {
      if (res && res.data) {
        this.enableFlag.monitor = res.data;
      }
    })
    this.getdispatchWorktype()
  },
  watch: {
    list: {
      handler(newVal) {
        if (newVal && newVal.length) {
          this.calcuTable()
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    setFsyncFlag(row, column) {
      if (row.fsyncFlag === 'Y') {
        return '同步'
      } else if (row.fsyncFlag === 'N') {
        return '不同步'
      } else {
        return ''
      }
    },
    //设置命令号
    setFcsNo(row, column) {
      if (row.fscNo) {
        return row.fscNo;
      }
      if (row.fscId) {
        return '申请中';
      } else {
        return '';
      }
    },
    //设置命令号
    setFwmNo(row, column) {
      if (row.fwmNo) {
        return row.fwmNo;
      }
      if (row.fwmId) {
        return '申请中';
      } else {
        return '';
      }
    },
    // 获取审核配置
    getAuditConfigurationByPage() {
      let data = {
        fywType: this.$route.name
      }
      getAuditConfigurationListByPage(data).then(res => {
        this.examConfig = res.data;
      })
    },
    //销号
    async handleXH() {
      if (this.multipleSelection.length !== 1) {
        this.$modal.msgWarning("请选择一条记录！")
        return false;
      }
      const row = this.multipleSelection[0];
      const status = row.fstatus;
      const fflag = row.fflag;
      if (status !== '9') {
        this.$modal.msgWarning("请选择完成审批的数据进行销号！");
        return false;
      }
      if (fflag === 'Y') {
        this.$modal.msgWarning("请选择未车间确认的数据销号！")
        return false;
      }
      const type = this.getScAndWnType(row);
      if (type === 'BOTH') {
        //调度命令+盯控
        if (!row.id || !row.fscId || !row.fscNo) {
          //必须得有调度命令
          this.$modal.msgWarning("请先申请命令号！")
          return false;
        }
        let scInfo;
        try {
          scInfo = await getScheduleCommand(row.fscId);
        } catch (e) {
          this.$modal.msgError("查询调度命令信息异常，请联系管理员！");
          return false
        }
        if (!scInfo?.data || scInfo.data.length === 0) {
          this.$modal.msgError("当前派工单未找到调度命令信息，请联系管理员！");
          return false;
        }
        const fwatch = scInfo?.data?.fwatch;
        //盯控不卡控
        // if (fwatch === 'Y') {
        //如果有盯控
        let wmInfo;
        try {
          wmInfo = await getWorkMonitorByDispatchId(row.id);
        } catch (e) {
          this.$modal.msgWarning("查询盯控信息异常，请联系管理员！");
          return false;
        }
        // if (!wmInfo?.data || wmInfo.data.length === 0) {
        //   this.$modal.msgError("当前派工单未找到盯控信息，请联系管理员！");
        //   return false;
        // }
        if (wmInfo?.data && wmInfo.data.length === 1) {
          const wmFstatus = wmInfo?.data?.fstatus;
          if (!["W", "CL"].includes(wmFstatus)) {
            this.$modal.msgWarning("当前派工单需要盯控，请先盯控结束或者取消盯控才能销号！");
            return false
          }
        }

        // }
      }
      if (type === 'SC') {
        if (!row.fscId || !row.fscNo) {
          this.$modal.msgWarning("请先申请命令号！");
          return false;
        }
      }
      if (type === 'WM') {
        //盯控不卡控
        // if (!row.fwmId || !row.fwmNo) {
        //   this.$modal.msgWarning("请先申请盯控！");
        //   return false;
        // }
      }
      const config = await getConfigKey(row.fworkareaId, PROD_DISPATCH.PROD_DISPATCH_XH_TYPE)
      let xhType = 'N';
      if (config && config.data) {
        xhType = config.data
      }
      const ruleRes = await getConfigKey(row.fworkareaId, PROD_DISPATCH.PRO_DISPATCH_XH_RULES);
      const xhRule = ruleRes?.data || PROD_DISPATCH.RULES.CD;
      this.saleOpen = true;
      this.title = '派工单-' + row.fdispatchNo
      const data = row
      data.xhRule = xhRule
      console.log(data, "data")
      this.$nextTick(() => {
        this.$refs.salesNumber.getData(data, xhType, false);
      })
    },
    handleConfirm(type) {
      const row = this.multipleSelection;
      let disabled = false;
      if (row.length !== 1) {
        this.$modal.msgWarning("请选择一条记录！")
        return false;
      }
      if (type === 'confirm') {
        const status = row[0].fstatus;
        const fxhStatus = row[0].fxhStatus;
        if (status !== '9') {
          this.$modal.msgWarning("请选择完成审批的数据进行销号！");
          return false;
        }
        if (fxhStatus !== 'Y') {
          this.$modal.msgWarning("请先销号完成再车间确认！");
          return false;
        }
        const fflag = row[0].fflag;
        if (fflag === 'Y') {
          disabled = true;
        }
        this.confirmOpen = true;
        const data = row[0];
        const option = 'C'  //车间确认默认退回
        this.$nextTick(() => {
          this.$refs.confirmSales.getData(data, disabled, option);
        })
      } else if (type === 'cancel') {
        const fflag = row[0].fflag;
        if (fflag !== 'Y') {
          this.$modal.msgWarning("仅能取消已确认的数据！");
          return false;
        }
        this.$modal.confirm('是否确认取消选中数据的车间确认?').then(function () {
          return cancelWorkshopConfirm({id: row[0].id});
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("取消成功");
        });
      }
    },
    //查看调度命令
    onClick(row) {
      // let fcsdeptId = this.$store.getters.fcsDeptId;
      // let fip = location.hostname;
      // getDept(row.fdeptId).then(res => {
      //   fcsdeptId = res.data.fcsdeptId;
      // })
      const fscId = row.fscId;
      const fscNo = row.fscNo;
      if (!fscNo) {
        this.$modal.msgWarning("请选择有命令号的数据查看！");
        return false;
      }
      this.viewSecheduleFlag = true;
      this.$nextTick(() => {
        this.$refs.viewSchedule.openThisView(fscId);
      })
      // let deptId = ''
      // getScheduleCommand(fscId).then(res => {
      //   let fip = '*************';
      //   let deptId = ''
      //   const {data} = res;
      //   const level = data.fsubmitLevel;
      //   const fworkshopId = data.fworkshopId;
      //   const fcsdeptId = data.fcsdeptId;
      //   if (level === 'WORKSHOP') {
      //     deptId = fworkshopId;
      //   } else if (level === 'CS') {
      //     deptId = fcsdeptId;
      //   }
      //   if (!!row.fscNo) {
      //     this.$router.push({
      //       path: '/produce/raqsoft/调度命令预览',
      //       query: {
      //         params: `produce/调度命令.rpx&fid=${row.fscId}&fcsdeptId=${deptId}&fip=${fip}`,
      //       },
      //     })
      //   }
      // })
      // if (!!row.fscNo) {
      //   //this.$router.push({
      //   //  name: 'ProduceRaqsoft',
      //   //  query: {
      //   //    params: `produce/调度命令.rpx&fid=${row.fscId}&fcsdeptId=${fcsdeptId}`,
      //   //  },
      //   //})
      //   this.$router.push({
      //     path: '/produce/raqsoft/调度命令预览',
      //     query: {
      //       params: `produce/调度命令.rpx&fid=${row.fscId}&fcsdeptId=${fcsdeptId}&fip=${fip}`,
      //     },
      //   })
      // }
    },
    //查看盯控
    async onWmNoClick(row) {
      const fscId = row.fscId;
      const fscNo = row.fwmNo;
      const fdate = row.fdate;
      if (!fscNo) {
        this.$modal.msgWarning("请选择有命令号的数据查看！");
        return false;
      }
      await this.$router.push({
        name: 'WorkMonitor',
        query: {
          fwmNo: fscNo,
          btn: 'edit',
          fdate: fdate
        }
      })
    },
    // 选中的数据
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log(val, "val");
    },

    // 行唯一键，配合 reserve-selection 跨页保留选择
    getRowKey(row) {
      return row.id;
    },

    // 勾选单个复选框时，联动选中/取消该 fdismainid 组内的所有行
    handleSelect(selection, row) {
      if (this.isBulkSelecting) return;
      const isSelected = selection.some(s => s.id === row.id);

      // 找到同一 fdismainid 的所有行
      const groupRows = this.list.filter(r => r.fdismainid === row.fdismainid);

      // 批量联动切换
      this.isBulkSelecting = true;
      this.$nextTick(() => {
        groupRows.forEach(r => {
          this.$refs.multipleTable.toggleRowSelection(r, isSelected);
        });
        this.isBulkSelecting = false;
      });
    },
    // 选择工区
    setWorkArea(value) {
      let [item] = value;
      this.queryParams.fdeptId = item.id;
      console.log("this.queryParams.fdeptId=", this.queryParams.fdeptId);
      this.handleOrgChange(value);
    },
    handleBatchExamine(fbtnum) {
      if (this.multipleSelection.length === 0) {
        this.$modal.msgWarning('请至少选择一条数据!');
        return false;
      }
      let ids = this.multipleSelection.map(item => item.id);
      this.$router.push({
        name: 'BatchDispatchApproval',
        query: {
          ids: ids
        }
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.addDispatchOpen = true;
      this.addDispatchTitle = '添加派工单';
      let dialogFormData = {
        //isEdit: true,
        //form: this.form
        type: 'type'
      }
      this.$nextTick(() => {
        this.$refs.AddDispatch.pushDialogFormData(dialogFormData);
      });
    },
    handleAddDispatchClose() {
      this.$refs.AddDispatch.reset();
      this.$refs.AddDispatch.keys.station = Date.now();
      this.$refs.AddDispatch.keys.dept = Date.now();
      this.addDispatchOpen = false;
    },
    // 根据日计划id查询明细
    queryPlanDayDetailList(id) {
      return new Promise((resolve, reject) => {
        getPlanDayDetailListAll({fmainId: id}).then(res => {
          resolve(res)
        }).catch((err) => {
          reject(err)
        })
      })
    },
    //添加派工单按钮
    async handleJump(obj) {
      this.handleAddDispatchClose()
      let res = await this.queryPlanDayDetailList(obj.fmainId)
      let detailList = res.data
      let dispatchList = detailList.filter(item => {
        return !!item.fdispatchNo;
      })
      if (dispatchList.length > 0) {
        this.$modal.msgWarning('已派工的设备不可继续派工!');
        return false;
      }
      let data = {
        fmainId: obj.fmainId,
        fdate: new Date(obj.fdate).getTime(),
        fstationnIds: obj.flocationIds,
        fstatioNames: obj.flocation,
        equList: []
      }
      // if (this.showModelGroup == PROD_PLAN_DAY.WEEK_DAY_GROUP_MODEL_OPTION[0].value) {
      //   //如果日计划合并删除的话，重组删除id
      //   detailList.forEach(item => {
      //     let detailids = item.detailids.split(",");
      //     if (detailids.length > 0) {
      //       detailids.forEach(id => {
      //         console.log(id);
      //         let map = {};
      //         map.id = id;
      //         data.equList.push(map);
      //       })
      //     }
      //   })
      // } else {
      //   data.equList = detailList;
      // }
      data.equList = detailList;
      console.log("申请派工参数data", data);
      saveOrUpdatePlanDayDispatch(data).then(res => {
        const id = res.data;
        this.openDispatch = true;
        this.titleDispatch = '申请派工单';
        let dispatchData = {
          ids: id,
          btn: 'edit'
        }
        this.$nextTick(() => {
          this.$refs.viewDispatchOrder.getData(dispatchData);
        })
      })
    },
    async handleExamine(fbtnum, buttonType) {
      if (this.multipleSelection.length <= 0) {
        this.$modal.msgWarning('至少选择一条数据!');
        return false;
      }
      let row = this.multipleSelection[0];
      let rows = this.multipleSelection;
      // 验证派工单明细是否已审批通过，如存在未审批通过的
      let fsyncArr = rows.filter(item => item.fsyncFlag != 'N')
      if (fsyncArr != null && fsyncArr.length > 0) {
        this.$modal.msgWarning("日计划未审批");
        return false;
      }
      if (buttonType == '1' || buttonType == '2' || buttonType == '3') {
        let rows = this.multipleSelection;
        // 审核或审批时验证派工单状态
        let statusArr = rows.filter(item => parseInt(item.fstatus) < 1)
        if (statusArr != null && statusArr.length > 0) {
          this.$modal.msgWarning("派工单状态不符，请先提交");
          return false;
        }
      }
      // 验证同一部门
      const deptArr = rows.some(item =>
        item.fdeptId != row.fdeptId);
      if (deptArr) {
        this.$modal.msgWarning("请选择同一部门的派工单");
        return false;
      }
      // 验证作业日期
      const dateArr = rows.some(item =>
        item.fdate != row.fdate);
      if (dateArr) {
        this.$modal.msgWarning("请选择同一作业日期的派工单");
        return false;
      }
      // const res = await validatePlanDispatch({ ids: rows.map(item => item.id) });
      // const flag = res.data;
      // if (!flag) {
      //   this.$modal.msgWarning("派工单存在未填写的信息！");
      //   return false;
      // }
      // validatePlanDayDetailStatusExists({ ids: rows.map(item => item.id) }).then(res => {
      //   if (!!res.data) {
      //     this.$modal.msgWarning(res.data);
      //     return false;
      //   } else {

      //   }
      // })
      //endregion
      // if (this.varifyItem(row)) {
      //   return false;
      // }
      if (!Array.isArray(fbtnum)) {
        let audit = auditVerification(row.fstatus, row.fosType, fbtnum, this.examConfig);
        if (!audit.status) {
          this.$modal.msgWarning(audit.notice);
          return false;
        }
      }
      if (row.fstatus < 1 || Array.isArray(fbtnum)) {
        if (!!fbtnum && !Array.isArray(fbtnum)) {
          let examData = {
            id: row.id,
            ids: rows.map(item => item.id),
            fywType: 'PlanDispatch',
            fosType: '',
            fstatus: row.fstatus,
            fworkareaId: row.fdeptId,
            fworkareaName: row.fdeptName,
          }
          this.examAll(this, 'baseExamUserTransfer', examData);
          return false;
        }
        let bigForm = {
          fdate: null,  //日期
          id: row.id,
          ids: rows.map(item => item.id),
          fywType: 'PlanDispatch',
          fpage: "PlanDayExamine",
          fparam: JSON.stringify({id: row.id, type: 'dispatch'}),
          fstatus: null,
          ffstatus: row.fstatus, // 当前的审核状态
          fopinion: "",
          fopinionType: "Y",
          fdeptId: this.$store.getters.deptInfo.id,
          finfoDeptId: this.$store.getters.deptInfo.id,
          fdeptName: this.$store.getters.deptInfo.name,
          fuserId: this.$store.getters.userId,
          fuserName: this.$store.getters.nickname,
          remark: "",
          fworkareaId: row.fdeptId,
          fworkareaName: row.fdeptName,
          selectedUserId: fbtnum[0].id,
          selectedUserName: fbtnum[0].nickname,
          sourceType: 'dispatch'  //派工单提交验证来源，验证日计划明细是否审批通过
        }
        submitDispatch(bigForm).then(res => {
          this.getList();
        })
        return false;
      }
      // 验证提交审批审核时派工单状态是否一致
      const statusArr = rows.some(item =>
        item.fstatus != row.fstatus);
      if (statusArr) {
        this.$modal.msgWarning("请选择状态相同的派工单");
        return false;
      }
      let examData = {
        id: row.id,
        fywType: "PlanDispatch",
        ids: rows.map(item => item.id),
        fstatus: "",
        ffstatus: row.fstatus,
        fdeptId: this.$store.getters.deptInfo.id,
        fdeptName: this.$store.getters.deptInfo.name,
        finfoDeptId: row.fdeptId,
        finfoDeptName: row.fdeptName,
        fuserId: this.$store.getters.userId,
        fuserName: this.$store.getters.nickname
      }
      baseAuditBeforeSubmit(examData).then(res => {
        if (rows.length > 1) {
          this.$router.push({
            name: 'BatchDispatchApproval',
            query: {
              id: row.id,
              type: 'dispatch',
              ids: rows.map(item => item.id).join(',')
            }
          })
        } else {
          this.$router.push({
            name: 'PlanDayExamine',
            query: {
              id: row.id,
              type: 'dispatch',
              ids: rows.map(item => item.id).join(',')
            }
          })
        }
      })
    },
    //申请命令号
    async handleApproveCommand() {
      const row = this.multipleSelection;
      if (row.length <= 0) {
        this.$modal.msgWarning("请最少选择一条数据")
        return false;
      }
      const status = row.filter(item => item.fstatus !== '9');
      if (status.length) {
        this.$modal.msgWarning("请选择审核状态为通过的数据申请命令号！");
        return false;
      }
      const data1 = row.filter(item => item.fscId !== null)
      if (data1.length) {
        this.$modal.msgWarning("请选择没有命令号的数据")
        return false;
      }
      const fFlagMark = row.some(item => item.fflag == 'Y')
      if (fFlagMark) {
        this.$modal.msgWarning("已经车间确认的数据不能申请调度命令！");
        return false;
      }
      const data2 = row.filter(item => item.fuseScheduleCommand === 'N')
      if (data2 && data2.length > 0) {
        var str = data2.map(item => item.fdispatchNo).join(', ');
        this.$alert('包含不启用调度命令的派工单，派工单号：' + str, '提示', {
          confirmButtonText: '确定',
          callback: action => {
          }
        });
        return false;
      }
      const fdate = row.filter(item => item.fdate !== row[0].fdate);
      const fdept = row.filter(item => item.fdeptId !== row[0].fdeptId);
      const fstation = row.filter(item => item.fstationIds !== row[0].fstationIds);
      if (fdate.length !== 0 || fdept.length !== 0 || fstation.length !== 0) {
        this.$modal.msgWarning("选择的数据的工区或日期或车站不一致")
        return false;
      }
      this.commandOpen = true;
      let data = row;
      let approvalForm = this.bigForm;
      let id = null;
      let fsubmitLevel = null;
      const res = await getConfigKey(row[0].fworkareaId, PROD_SCHEDULE_COMMAND.UNIFIED_CONFIGURATION)
      const fconfig = JSON.parse(res.data);
      fsubmitLevel = fconfig[0]?.fdefaultValue ? fconfig[0]?.fdefaultValue : null;
      //获取工作内容来源配置
      const fcontentConfig = await getConfigKey(row[0].fworkareaId, PROD_DISPATCH.PROD_SC_MONITOR_FCONTENT_SOURCE)
      let fcontentType = fcontentConfig?.data ? fcontentConfig.data : '作业项目' //默认取作业项目
      const form = {
        fworkareaId: row[0].fworkareaId,
        fworkareaName: row[0].fworkareaName,
        fonline: 'Y',
        fwatch: 'Y',
        fsubmitLevel: fsubmitLevel,
        disabledFlag: fsubmitLevel ? true : false,
        fcontentType: fcontentType,
      }
      createScheduleCommand().then(res => {
        id = res.data;
        console.log("id", id)
        this.$nextTick(() => {
          this.$refs.scheduleCommand.getData(data, approvalForm, id, form, fconfig);
        })
      })
    },
    //申请作业盯控
    async handleApproveMonitor() {
      const row = this.multipleSelection;
      if (row.length <= 0) {
        this.$modal.msgWarning("请最少选择一条数据")
        return false;
      }
      const status = row.filter(item => item.fstatus !== '9');
      if (status.length) {
        this.$modal.msgWarning("请选择审核状态为通过的数据申请命令号！");
        return false;
      }
      const fflag = row.some(item => item.fwmNo != null);
      if (fflag) {
        this.$modal.msgWarning("当前数据已经申请盯控！");
        return false;
      }
      const fFlagMark = row.some(item => item.fflag == 'Y')
      if (fFlagMark) {
        this.$modal.msgWarning("已经车间确认的数据不能申请盯控！");
        return false;
      }
      // 判断是否启用调度命令
      const schedule = row.filter(item => item.fuseScheduleCommand === 'Y');
      if (schedule != null && schedule.length > 0) {
        var str = schedule.map(item => item.fdispatchNo).join(', ');
        this.$alert('启用调度命令的派工单不能由此申请盯控，派工单号：' + str, '提示', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
        return false;
      }
      // 盯控不卡控
      // const workMonitor = row.filter(item => item.fwatch !== 'Y');
      // if (workMonitor != null && workMonitor.length > 0) {
      //   var workStr = workMonitor.map(item => item.fdispatchNo).join(', ');
      //   this.$alert('包含不盯控的派工单，派工单号：' + workStr, '提示', {
      //     confirmButtonText: '确定',
      //     callback: action => {
      //     }
      //   });
      //   return false;
      // }

      const fxhFlag = row.some(item => item.fxhStatus == 'Y');
      if (fxhFlag) {
        this.$modal.msgWarning("当前数据已经销号，不能申请盯控！");
        return false;
      }
      const hasMismatch = row.some(item =>
        item.fdate !== row[0].fdate ||
        item.fdeptId !== row[0].fdeptId ||
        item.fstationIds !== row[0].fstationIds
      );
      if (hasMismatch) {
        this.$modal.msgWarning("选择的数据的工区或日期或车站不一致");
        return false;
      }
      //获取工作内容来源配置
      const fcontentConfig = await getConfigKey(row[0].fworkareaId, PROD_DISPATCH.PROD_SC_MONITOR_FCONTENT_SOURCE)
      let fcontentType = fcontentConfig?.data ? fcontentConfig.data : '作业项目' //默认取作业项目
      this.addWorkMonitorOpen = true;
      let data = row;
      this.$nextTick(() => {
        this.$refs.addWorkMonitor.getData(data, fcontentType);
      })
    },
    AddWorkMonitorEditOk() {
      this.addWorkMonitorOpen = false;
      this.getList()
    },
    dispatchEditOk(type) {
      if (type === 'add') {
        this.openDispatch = false;
        this.getList();
      } else if (type === 'copy') {
        this.getList();
      } else if (type === 'close') {
        this.openDispatch = false;
      }
    },
    //查看派工单
    handlePgShow(row) {
      //this.$router.push({
      //  //name: 'PlanDayItemDispatch',
      //  path: '/produce/wxtc/planDayItemDispatch/查看派工单',
      //  query: {
      //    id: row.id,
      //    pageName: 'PlanDispatch',
      //    btn: 'look'
      //  }
      //})
      this.openDispatch = true;
      this.titleDispatch = '查看派工单';
      let data = {
        ids: row.id,
        btn: 'look'
      }
      this.$nextTick(() => {
        this.$refs.viewDispatchOrder.getData(data);
      })
    },
    //查看防护单
    // handleShow() {//参数调整后无效
    //   if (this.multipleSelection.length !== 1) {
    //     this.$modal.msgError("只能选中一条数据");
    //     return false;
    //   }
    //   const row = this.multipleSelection[0];
    //   let beginTime = this.parseTime(row.ftimeBegin, '{y}-{m}-{d} {h}:{i}')
    //   let endTime = this.parseTime(row.ftimeEnd, '{y}-{m}-{d} {h}:{i}')
    //   let fdate = new Date(row.fdate);
    //   let nowDate = new Date();
    //   let fileName = '现场防护单-' + this.parseTime(new Date(fdate.getFullYear(), fdate.getMonth(), fdate.getDate(), nowDate.getHours(), nowDate.getMinutes(), nowDate.getSeconds()).getTime(), '{y}{m}{d}{h}{i}{s}');
    //   this.$router.push({
    //     path: '/produce/raqsoft/现场防护单预览',
    //     query: {
    //       params: `produce/现场防护单.rpx&fileName=${fileName}&dispatchId=${row.id}&mainId=${row.fmainId}&stationId=${row.fstationIds}&beginTime=${beginTime}&endTime=${endTime}&fprotectIds=${row.fprotectIds}`,
    //     },
    //   })
    // },
    //查看防护单
    // handleShowZZ() {
    //   if (this.multipleSelection.length !== 1) {
    //     this.$modal.msgError("只能选中一条数据");
    //     return false;
    //   }
    //   const row = this.multipleSelection[0];
    //   let beginTime = this.parseTime(row.ftimeBegin, '{y}-{m}-{d} {h}:{i}')
    //   let endTime = this.parseTime(row.ftimeEnd, '{y}-{m}-{d} {h}:{i}')
    //   let fdate = new Date(row.fdate);
    //   let nowDate = new Date();
    //   let fileName = '驻站防护单-' + this.parseTime(new Date(fdate.getFullYear(), fdate.getMonth(), fdate.getDate(), nowDate.getHours(), nowDate.getMinutes(), nowDate.getSeconds()).getTime(), '{y}{m}{d}{h}{i}{s}');
    //   this.$router.push({
    //     path: '/produce/raqsoft/驻站防护单预览',
    //     query: {
    //       params: `produce/驻站防护单.rpx&fileName=${fileName}&dispatchId=${row.id}&mainId=${row.fmainId}&stationId=${row.fstationIds}&beginTime=${beginTime}&endTime=${endTime}&linkIds=${row.flinkIds}`,
    //     },
    //   })
    // },
    // // 派工单查看
    // handleShowDispatch() {
    //   if (this.multipleSelection.length !== 1) {
    //     this.$modal.msgError("只能选中一条数据");
    //     return false;
    //   }
    //   const row = this.multipleSelection[0];
    //   let fileName = '派工单-' + new Date(row.fdate).getFullYear() + '-' + row.fdispatchNo;
    //   this.$router.push({
    //     path: '/produce/raqsoft/派工单预览',
    //     query: {
    //       params: `produce/派工单.rpx&mainId=${row.id}&scale=1.6&fileName=${fileName}`,
    //     },
    //   })
    // },
    //派工单修改
    handleShowDis(row) {
      this.openDispatch = true;
      this.titleDispatch = '查看派工单';
      let data = {
        ids: row.id,
        btn: 'look'
      }
      this.$nextTick(() => {
        this.$refs.viewDispatchOrder.getData(data);
      })
    },
    // 兑现
    handleCash() {
      if (this.multipleSelection.length === 0) {
        this.$modal.msgWarning('仅能选择一条数据!');
        return false;
      }
      let row = this.multipleSelection[0];
      this.cashOpen = true;
      let data = {
        fmainId: row.id
      }
      this.$nextTick(() => {
        this.$refs.PlanDayCash.getData(data);
      })
    },
    //关闭销号弹窗
    saleEditOk() {
      this.$refs.salesNumber.$refs.queryForm.resetFields();
      this.getList()
      this.saleOpen = false;
    },
    //关闭车间确认弹窗
    confirmEditOk() {
      this.getList()
      this.confirmOpen = false;
    },
    //关闭命令申请弹窗
    commandEditOk() {
      this.$refs.scheduleCommand.$refs.form.resetFields();
      this.getList()
      this.commandOpen = false;
    },
    // 关闭兑现
    cashEditOk() {
      this.$refs.PlanDayCash.$refs.cashForm.resetFields();
      this.$refs.PlanDayCash.cashForm.fskyFlag = 'Y';
      this.getList()
      this.cashOpen = false;
    },
    /** table 序号 **/
    indexMethod(index) {
      return (this.queryParams.pageNo - 1) * this.queryParams.pageSize + index + 1;
    },
    /** 查询列表 */
    getList() {
      // this.list = [];
      // this.total = 0;
      this.loading = true;
      // 执行查询
      getPlanDayDispatchPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      }).finally(() => {
        this.$nextTick(() => {
          this.$refs.multipleTable.doLayout();
        })
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // if (this.queryParams.fworkItemList && this.queryParams.fworkItemList.length > 0) {
      //   this.queryParams.fworkItem = this.queryParams.fworkItemList.join(',');
      // }
      this.$refs.queryForm.validate((valid, failFiled) => {
        if (valid) {
          this.queryParams.fmainId = undefined
          this.queryParams.pageNo = 1;
          this.getList();
        } else {
          const errorMsg = Object.values(failFiled)
            .map(item => item[0].message)
            .join(',')
          if (!!errorMsg) {
            this.$modal.msgError(errorMsg)
          }
        }
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.stationKey = Date.now();
      this.resetForm("queryForm");
      this.queryParams.fmainId = null;
      this.queryParams.fworkItemList = [];
      this.handleQuery();
    },
    // 批量作废
    async handleCancelAll() {
      if (this.multipleSelection.length !== 1) {
        this.$modal.msgWarning('请选择一条数据作废!');
        return false;
      }
      const row = this.multipleSelection[0];
      if (row.fstatus !== '9') {
        this.$modal.msgWarning("审核未通过的数据不能作废！");
        return false;
      }
      if (row.fflag === 'Y') {
        this.$modal.msgWarning("已车间确认数据不能作废！");
        return false;
      }
      const type = this.getScAndWnType(row); //获取派工单配置
      if (!this.checkCancel(type, row)) return false; //校验
      await this.checkDispatchRecall(this.multipleSelection, '作废');
      let ids = this.multipleSelection.map(item => item.id);

      this.$modal.confirm('是否确认作废当前选中的的派工单?').then(function () {
        return batchInvalidPlanDayDispatch({ids: ids});
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("作废成功");
      }).catch(() => {

      })
    },
    // 批量删除
    handleDeleteAll() {
      if (this.multipleSelection.length !== 1) {
        this.$modal.msgWarning('请选择一条数据删除!');
        return false;
      }
      const row = this.multipleSelection[0];
      const status = row.fstatus;
      const invalidStatuses = ['0', '-1', '-2', '-9', '10'];  //非法状态
      if (!invalidStatuses.includes(status)) {
        this.$modal.msgWarning("只能选择状态为保存、退回、作废的数据删除！");
        return;
      }
      if (this.multipleSelection.length !== 1) {
        this.$modal.msgWarning('请选择一条数据!');
        return false;
      }
      // if (this.multipleSelection.filter(item => item.fstatus !== '-9').length > 0) {
      //   this.$modal.msgWarning('仅可删除作废数据!');
      //   return false;
      // }
      let ids = this.multipleSelection.map(item => item.id);
      this.$modal.confirm('是否确认删除当前选中的的派工单?').then(function () {
        return batchDeletePlanDayDispatch({ids: ids});
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {

      })
    },
    /** 变更审批人员 第三步添加按钮 */
    handleChangeApproval() {
      if (this.multipleSelection.length !== 1) {
        this.$modal.msgWarning("仅能选择一条数据");
      } else {
        let row = this.multipleSelection[0];
        if (Number(row.fstatus) > 0 && Number(row.fstatus) != 9) {
          if (row.fsyncFlag == 'N') {
            let queryData = {};
            queryData.fmodel = 'planDispatch';   // 流程编号每个流程不一样
            queryData.fywId = row.id;
            queryData.fworkareaId = row.fdeptId;
            queryData.fworkareaName = row.fdeptName;
            queryData.fstatus = row.fstatus;
            this.$nextTick(() => {
              this.$refs['BaseAuditUserTransfer'].pushQueryData(queryData);
            })
          } else {
            this.$modal.msgWarning("日计划未审批");
          }
        } else {
          this.$modal.msgWarning("不能变更审批人员");
        }

      }
    },
    handleOrgChange(val) {
      console.log("组织机构选择", val);
      const [item] = val;
      this.queryParams.fworkareaId = item.id;
      if (!!this.queryParams.fworkareaId) {
        let containsChar = item.name.indexOf("电务段") !== -1;   //段级部门查询不到车站/区间
        if (!containsChar) {
          this.getStationList(item.id);
        }
      } else {
        this.stationList = [];
      }
    },
    getStationList(fworkareaId) {
      let data = {
        fworkareaId: fworkareaId
      }
      getStationIntervalListByLD(data).then(res => {
        this.stationList = res.data.map(item => {
          return {id: item.stationId, fname: item.stationName, lineId: item.lineId, lineName: item.lineName};
        })
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有派工单数据项?').then(() => {
        this.exportLoading = true;
        return exportPlanDispatchExcel(params);
      }).then(response => {
        this.$download.excel(response, '派工单.xls');
        this.exportLoading = false;
      }).catch(() => {
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.$refs.queryForm.validate((valid, failFiled) => {
        if (valid) {
          this.queryParams.fmainId = null;
          this.queryParams.fdeptId = data.id;
          this.setWorkArea([data])
          this.getList();
        } else {
          const errorMsg = Object.values(failFiled).map(item => item[0].message).join(',')
          if (!!errorMsg) {
            this.$modal.msgError(errorMsg)
          }
        }
      })
    },
    //审批流程按钮点击事件
    handleApprovalProcess(row, column) {
      // 只有点击的列是状态才显示
      if (column.property === 'fstatus') {
        this.$refs.ApprovalProcess.getExam(row);
      } else if (column.property === 'fxhStatus') {
        this.saleOpen = true;
        this.title = '派工单-' + row.fdispatchNo
        const data = row
        const show = true
        data.confirmColumn = 'doubleClick'
        this.$nextTick(() => {
          this.$refs.salesNumber.getData(data, null, show);
        })
      } else {
        return
      }
    },
    handleShowCash() {
      const row = this.multipleSelection[0];
      if (this.multipleSelection.length !== 1) {
        this.$modal.msgWarning("请选择一条记录查看兑现情况！");
        return false;
      }
      this.saleOpen = true;
      this.title = '派工单-' + row.fdispatchNo
      const data = row
      const show = true
      this.$nextTick(() => {
        this.$refs.salesNumber.getData(data, null, show);
      })
    },
    async handleShowDispatch(row) {
      this.viewDispatchFlag = true;
      const data = row;
      this.$nextTick(() => {
        this.$refs.viewDispatch.openThisView(data, null);
      })
    },
    closeAllDialog() {
      this.viewDispatchFlag = false;
      this.viewSecheduleFlag = false;
    },
    setDangerLevel(row) {
      const dangerLevel = this.getDictDatas(DICT_TYPE.PRO_DANGER_LEVEL)
      const ftype = row.fdangerLevel;
      for (let i of dangerLevel) {
        if (ftype == i.value) {
          if (i.label == '高') {
            return '<span class="el-tag el-tag--danger el-tag--dark  custom-high-tag">' + i.label + '</span>';
          } else if (i.label == '中') {
            return '<span class="el-tag el-tag--warning el-tag--dark custom-middle-tag">' + i.label + '</span>';
          } else if (i.label == '低') {
            return '<span class="el-tag el-tag--primary el-tag--dark custom-low-tag">' + i.label + '</span>';
          }
        }
      }
      return '';
    },
    editOk() {
      this.getList()
      this.openTcplanDetail = false
      this.openTdmsDetail = false;
      this.openConstructionDetail = false;
      this.addDispatchOpen = false
    },
    showFplan(row) {
      let type = row.fworkKind;
      let id = row.fsourceId;
      if (type === '1') {
        getPlanTdms(id).then((response) => {
          let form = response.data;
          this.openTdmsDetail = true;
          let dialogFormData = {
            isEdit: false,
            form: form,
          };
          this.$nextTick(() => {
            this.$refs.addDetail.pushDialogFormData(dialogFormData);
          });
        });
      } else if (type === '2') {
        getPlanTdmsConstruction(id).then((response) => {
          let form = response.data;
          this.openConstructionDetail = true;
          let dialogFormData = {
            isEdit: false,
            form: form,
          };
          this.$nextTick(() => {
            this.$refs.addDetailConstruction.pushDialogFormData(dialogFormData);
          });
        });
      } else if (type === '9') {
        getTcplan(id).then(response => {
          let form = response.data;
          this.openTcplanDetail = true;
          this.title = "查看临时天窗计划";
          let dialogFormData = {
            isEdit: false,
            form: form
          }
          this.$nextTick(() => {
            this.$refs.addTcplanDetail.pushDialogFormData(dialogFormData);
          });
        });
      }
    },
    async handleRecall() {
      const rows = this.multipleSelection;
      if (rows.length < 1) {
        this.$message.warning('请至少选择一条数据');
      }
      const canRecall = rows.every(row => +row.fstatus > 0 || +row.fstatus < 9);
      if (!canRecall) {
        this.$message.warning('请选择在审核中的数据');
      }
      try {
        await this.$modal.confirm(`是否确认撤回 ${rows.map(item => item.fdeptName).join(',')} 派工单号为【${rows.map(item => item.fdispatchNo).join(',')}】的数据项?`);
        await recallDispatch(rows.map(item => item.id))
        this.getList()
        this.$message.success('撤回成功！')
      } catch (error) {
        this.$message.warning('取消撤回！')
      }
    },
    getdispatchWorktype() {
      listDispatchWorktypeDict().then(res => {
        this.worktypeList = res.data;
      })
    },
    useDispatchType(row, column) {
      for (const type of this.worktypeList) {
        if (row.fworkKind === type.value) {
          return type.label;
        }
      }
    },
    //根据盯控状态来判断是否可以做message的操作
    async checkDispatchRecall(row, message) {
      const fwmIdNomap = new Map(
        row
          .filter(item => item.fwmId && item.fdispatchNo)
          .map(item => [item.fwmId, item.fdispatchNo])
      );
      const wmIds = row.filter(item => item.fwatch === 'Y' && item.fwmId)
        .map(item => item.fwmId);
      if (wmIds.length === 0) {
        return true; // 没有盯控，不影响
      }
      try {
        const wmInfo = await getWorkMonitorListByIds(wmIds);
        const wmData = wmInfo?.data || [];
        const hasRecalled = wmData.filter(item => item.fstatus !== 'CCL').map(item => fwmIdNomap.get(item.id)).filter(Boolean);
        if (hasRecalled && hasRecalled.length > 0) {
          const msg = `派工单${hasRecalled.join(',')}已申请盯控，不能${message}`;
          this.$modal.msgError(msg);
          return false;
        }
        return true;
      } catch (error) {
        // this.$modal.msgError('校验盯控状态失败');
        return false;
      }
    },
    getScAndWnType(row) {
      const {fuseScheduleCommand, fwatch} = row;
      if (fuseScheduleCommand === 'Y' && fwatch === 'Y') return 'BOTH';
      if (fuseScheduleCommand === 'Y') return 'SC';
      if (fwatch === 'Y') return 'WM';
      return null;
    },
    checkCancel(type, row) {
      const checkMap = {
        SC: {field: 'fscId', msg: '已申请调度命令，不允许作废！'},
        WM: {field: 'fwmId', msg: '已申请盯控，不允许作废！'},
        BOTH: {field: 'fscId', msg: '已申请命令号，不允许作废！'},
      };
      const cfg = checkMap[type];
      if (cfg && row[cfg.field]) {
        this.$modal.msgWarning(cfg.msg);
        return false;
      }
      return true;
    },
    // 针对单独某一列生成 spanArr
    // 根据 fdismainid 计算合并数组，只用于 fstatus 和 fdispatchNo 列
    buildSpanArr(data) {
      const spanArr = [];
      let pos = 0;
      data.forEach((item, i) => {
        if (i === 0) {
          spanArr.push(1);
          pos = 0;
        } else {
          if (item.fdismainid === data[i - 1].fdismainid) {
            spanArr[pos] += 1;
            spanArr.push(0);
          } else {
            spanArr.push(1);
            pos = i;
          }
        }
      });

      // 只为需要合并的列设置 spanArr
      this.spanArrMap['fstatus'] = [...spanArr];
      this.spanArrMap['fdispatchNo'] = [...spanArr];
    },
    // 在 methods 中添加获取自定义序号的方法
    getCustomIndex(rowIndex) {
      // 找到当前行所属的合并组的第一行
      const arr = this.spanArrMap["fstatus"] || [];
      let groupIndex = 1;

      for (let i = 0; i <= rowIndex; i++) {
        if (arr[i] > 0) { // 如果是合并组的第一行
          if (i === rowIndex) {
            return groupIndex;
          }
          groupIndex++;
        }
      }
      return groupIndex;
    },

    // 修改 spanMethod，只合并 fstatus 和 fdispatchNo
    spanMethod({column, rowIndex}) {
      const columnKey = column.property || column.type;
      // 只合并 fstatus 和 fdispatchNo 列
      if (["fdispatchNo", "fstatus"].includes(columnKey)) {
        const arr = this.spanArrMap[columnKey] || [];
        const _row = arr[rowIndex] || 0;
        return {
          rowspan: _row,
          colspan: _row > 0 ? 1 : 0
        };
      }
      // 其他列不合并
      return {rowspan: 1, colspan: 1};
    },
    calcuTable() {
      // 根据 fdismainid 计算 fstatus 和 fdispatchNo 的合并
      this.buildSpanArr(this.list);
    },
    handleEdit(type) {
      if (this.multipleSelection.length !== 1) {
        this.$modal.msgWarning("请选择一条数据！");
        return false;
      }
      const row = this.multipleSelection[0];
      if (type === 'edit') {
        if (((Number(row.fstatus) === 10) || (row.fscId !== null) || (row.fwmNo !== null) ||
          (Number(row.fstatus) > 0 && Number(row.fstatus) < 9) || row.fflag === 'Y'
        )) {
          this.$modal.msgWarning("该派工单不能修改！");
          return false;
        }
      }
      this.openDispatch = true;
      let msg = type === 'edit' ? '修改' : '查看';
      this.titleDispatch = `${msg}派工单`;
      let data = {
        ids: row.id,
        btn: type
      }
      this.$nextTick(() => {
        this.$refs.viewDispatchOrder.getData(data);
      })
    }
  },
  mounted() {
    //计算table合并列
    // 监听app-main组件的高度，动态设置table高度
    this.$erd.listenTo(
      document.getElementsByClassName("app-main"), (e) => {
        this.tableH = this.setTableHeight();
      });
  }
};
</script>

<style scoped>
.status-column {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px; /* Adjust spacing between dict-tag and buttons */
}

.vertical-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.el-tag {
  cursor: pointer; /* Change cursor to pointer for better UX */
}

::v-deep .custom-high-tag {
  background-color: #ff3737 !important;
  border-color: #ff3737 !important;
  color: white;
}

::v-deep .custom-middle-tag {
  background-color: #f1e00c !important;
  border-color: #f1e00c !important;
  color: black;
}

::v-deep .custom-low-tag {
  background-color: #a0aede !important;
  border-color: #a0aede !important;
  color: white;
}
</style>
